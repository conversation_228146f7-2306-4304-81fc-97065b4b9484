<template>
  <view class="content">
    <canvas
      type="2d"
      id="imageCanvas"
      :style="{ width: imgOriginInfo.width + 'px', height: imgOriginInfo.height }"
    ></canvas>
    <canvas type="2d" id="myCanvas" class="poster_canvas"></canvas>
    <view class="my-base"><button @click="onCreateAvatar">生成海报</button></view>
    <button
      open-type="share"
      class="bg-theme-brand text-white py-1 font-12 flex align-center justify-evenly rounded-8 card-box"
    >
      分享名片
    </button>
    <button @tap="chooseAndProcessImage">选择图片并处理</button>
    <view class="my-base">生成后的头像：</view>
    <image :src="avatar" mode="heightFix" class="width-100"></image>
    <view class="my-base">上传的图片：</view>
    <image :src="imageUrl" mode="heightFix" class="width-100"></image>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { onShareAppMessage, onLoad, onReady, onUnload } from '@dcloudio/uni-app'
const avatar = ref('')
const avatar_arrayBuffer = ref('')
const imgOriginInfo = reactive({
  width: 100,
  height: 100,
})
const imageUrl = ref('')
//用户点击分享
onShareAppMessage((e) => {
  let shareInfo = {
    path: `/pages/index/index?id=10`,
    title: `张三`,
    imageUrl: avatar.value,
  }
  return shareInfo
})
onReady(() => {
  // onCreateAvatar()
})
// 函数
function chooseAndProcessImage() {
  wx.chooseMedia({
    count: 1,
    mediaType: ['image'],
    success: (res) => {
      console.log('chooseMedia res', res)
      const imgUrl = res.tempFiles[0].tempFilePath
      imageUrl.value = imgUrl
      formatFaceData(imgUrl)
    },
  })
}
//  人脸识别
async function formatFaceData(imagePath = '') {
  uni.getImageInfo({
    src: imagePath,
    success: (res) => {
      const fixWidth = 300
      const { width, height } = res
      console.log('getImageInfo res', res)
      imgOriginInfo.width = width
      imgOriginInfo.height = height
      _session(imagePath)
    },
    fail: (res) => {
      console.error('getImageInfo:err', res)
    },
  })
}
async function _session(imagePath = '') {
  const session = uni.createVKSession({
    track: {
      face: { mode: 2 }, // mode: 1 - 使用摄像头；2 - 手动传入图像
    },
  })
  const canvas = wx.createOffscreenCanvas({
    type: '2d',
    width: imgOriginInfo.width,
    height: imgOriginInfo.height,
  })
  const ctx = canvas.getContext('2d')
  canvas.width = imgOriginInfo.width
  canvas.height = imgOriginInfo.height
  const img = canvas.createImage()
  await new Promise((resolve, reject) => {
    img.onload = resolve
    img.src = imagePath
    img.onerror = reject
  })
  ctx.clearRect(0, 0, imgOriginInfo.width, imgOriginInfo.height)
  ctx.drawImage(img, 0, 0, imgOriginInfo.width, imgOriginInfo.height)
  uni.canvasToTempFilePath({
    canvas: canvas,
    success(res) {
      avatar.value = res.tempFilePath
    },
  })
  const pixelData = ctx.getImageData(0, 0, imgOriginInfo.width, imgOriginInfo.height).data
  // const uint8ClampedArray = new Uint8ClampedArray(pixelData);
  // const arrayBuffer = uint8ClampedArray.buffer;
  const arrayBuffer = pixelData.buffer
  console.log('arrayBuffer', arrayBuffer)
  session.on('updateAnchors', (anchors) => {
    console.log('updateAnchors', anchors)
  })
  session.start((errno) => {
    if (errno) {
      console.log('errno', errno)
    } else {
      const result = session.detectFace({
        frameBuffer: arrayBuffer,
        width: imgOriginInfo.width,
        height: imgOriginInfo.height,
        scoreThreshold: 0.5,
        sourceType: 1,
        modelMode: 1,
      })
      session.on('addAnchors', (anchors) => {
        console.log('addAnchors', anchors)
      })
      console.log('成功', session.detectBody, result)
    }
  })
}
// 获取RGBA 数值
function handleImage(arrayBuffer, width = 100, height = 100) {
  const uint8Array = new Uint8Array(arrayBuffer)
  const data = new Uint8ClampedArray(uint8Array)
  const imageData = new ImageData(data, width, height)

  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  canvas.width = width
  canvas.height = height
  ctx.putImageData(imageData, 0, 0)

  const pixelData = ctx.getImageData(0, 0, width, height).data // 获取所有像素数据
  return pixelData
}
// 生成头像
function onCreateAvatar() {
  const query = wx.createSelectorQuery()
  query
    .select('#myCanvas')
    .fields({ node: true, size: true })
    .exec((res) => {
      const canvas = res[0].node
      const ctx = canvas.getContext('2d')

      const dpr = wx.getSystemInfoSync().pixelRatio
      const width = res[0].width
      const height = res[0].height
      console.log()
      canvas.width = width * dpr
      canvas.height = height * dpr
      ctx.scale(dpr, dpr)
      const bgImage = canvas.createImage()
      const img2 = canvas.createImage()
      const avatarImg = canvas.createImage()

      ctx.imageSmoothingEnabled = true
      bgImage.src = '/static/images/<EMAIL>'
      bgImage.crossOrigin = 'anonymous'
      bgImage.onerror = function () {
        console.log('图片加载失败，请尝试其他图片')
      }
      bgImage.onload = (e) => {
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        ctx.drawImage(bgImage, 0, 0, width, height)
        avatarImg.src = '/static/images/image.png'
        avatarImg.crossOrigin = 'anonymous'
        const avatarImgDx = width - 30 - 182
        const avatarImgDy = 24
        avatarImg.onload = (e) => {
          ctx.drawImage(bgImage, 0, 0, width, height)
          ctx.drawImage(avatarImg, avatarImgDx, avatarImgDy, 182, 284)
          img2.src = '/static/images/<EMAIL>'
          img2.crossOrigin = 'anonymous'
          const img2H = (width * 107) / 500
          const img2dy = height - img2H
          img2.onload = (e) => {
            ctx.drawImage(img2, 0, img2dy, width, img2H)
            drawText(ctx)
            uni.canvasToTempFilePath({
              canvas: canvas,
              success(res) {
                console.log('res', res)
                avatar.value = res.tempFilePath
              },
            })
          }
        }
      }
    })
  function drawText(ctx) {
    // 绘制名字
    ctx.font = `bold 36px 苹方-简 中粗体`
    ctx.fillStyle = '#092119'
    ctx.textAlign = 'left'
    ctx.textBaseline = 'middle'
    ctx.fillText('林薇', 53, 106)
    // 公司
    ctx.font = `18px 苹方-简 常规体`
    ctx.fillStyle = '#092119'
    ctx.textAlign = 'left'
    ctx.fillText('EaseInsur', 53, 160)
    ctx.fillText('高级销售经理', 53, 195)
  }
}
</script>

<style scoped lang="scss">
.poster_canvas {
  width: 750rpx; // 750 *600
  height: 600rpx;
  // position: fixed;
  // top: -10000rpx;
  left: 0rpx;
  box-sizing: border-box;
}
</style>
